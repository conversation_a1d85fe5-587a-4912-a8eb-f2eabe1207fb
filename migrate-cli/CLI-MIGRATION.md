# CLI 方式代码迁移

## 概述

现在 `codeMigrator.js` 已经改为使用 gogocode CLI 方式进行代码迁移，而不是直接调用 API。这样可以避免 API 调用时可能遇到的问题。

## 变更说明

### 主要变更

1. **移除了直接的 gogocode API 调用**
   - 不再直接 `require('gogocode')`
   - 不再直接 `require('gogocode-plugin-vue')`
   - 不再直接 `require('gogocode-plugin-element')`

2. **新增了 CLI 执行方式**
   - 使用 `child_process.spawn` 执行 gogocode 命令
   - 支持实时输出进度
   - 更好的错误处理

3. **简化了统计逻辑**
   - CLI 方式批量处理，统计更简单
   - 保留了原有的统计接口

### 执行的命令

迁移过程会依次执行以下命令：

```bash
# Vue 2 到 Vue 3 迁移
gogocode -s ./src -t gogocode-plugin-vue -o ./src

# Element UI 到 Element Plus 迁移  
gogocode -s ./src -t gogocode-plugin-element -o ./src
```

## 前置要求

### 全局安装 gogocode 和插件

```bash
# 安装 gogocode CLI
npm install -g gogocode

# 安装 Vue 迁移插件
npm install -g gogocode-plugin-vue

# 安装 Element UI 迁移插件
npm install -g gogocode-plugin-element
```

### 验证安装

```bash
# 检查 gogocode 是否安装成功
gogocode --version

# 检查插件是否安装成功
npm list -g gogocode-plugin-vue
npm list -g gogocode-plugin-element
```

## 使用方法

### 基本使用

```javascript
const CodeMigrator = require('./src/codeMigrator')

const migrator = new CodeMigrator('/path/to/your/project', {
    srcDir: 'src',
    backupDir: 'backup',
    includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
    excludePatterns: ['node_modules/**', 'dist/**', 'build/**']
})

const stats = await migrator.migrate()
```

### 测试迁移

运行测试脚本：

```bash
cd migrate-cli
node test-cli-migration.js
```

## 优势

### 相比 API 方式的优势

1. **更稳定**: 避免了 API 调用时的内存问题和错误处理复杂性
2. **更直观**: 直接使用官方推荐的 CLI 方式
3. **更好的错误信息**: CLI 输出的错误信息更清晰
4. **批量处理**: 一次性处理整个目录，效率更高

### 保留的功能

1. **备份功能**: 迁移前自动创建备份
2. **统计功能**: 提供迁移统计信息
3. **错误处理**: 完整的错误处理和日志
4. **配置选项**: 支持自定义源目录、排除模式等

## 注意事项

1. **确保 gogocode 全局安装**: CLI 方式需要全局安装 gogocode 和相关插件
2. **备份重要**: 迁移前会自动创建备份，但建议额外备份重要文件
3. **分步执行**: 先执行 Vue 迁移，再执行 Element UI 迁移
4. **检查结果**: 迁移完成后建议检查代码变更，确保符合预期

## 故障排除

### 常见问题

1. **命令不存在错误**
   ```
   Error: spawn gogocode ENOENT
   ```
   解决方案: 确保全局安装了 gogocode

2. **插件不存在错误**
   ```
   Error: Cannot find module 'gogocode-plugin-vue'
   ```
   解决方案: 确保全局安装了相应插件

3. **权限错误**
   ```
   Error: EACCES: permission denied
   ```
   解决方案: 检查文件权限，或使用 sudo 安装全局包

### 调试方法

1. 查看详细输出：CLI 执行过程中会显示实时进度
2. 检查错误日志：失败时会显示详细的错误信息
3. 手动测试：可以手动执行 gogocode 命令验证

## 后续计划

1. **增强统计**: 可以通过文件修改时间等方式提供更准确的统计
2. **并行处理**: 考虑并行执行多个插件以提高效率
3. **自定义插件**: 支持用户自定义的 gogocode 插件
4. **增量迁移**: 支持只迁移变更的文件
