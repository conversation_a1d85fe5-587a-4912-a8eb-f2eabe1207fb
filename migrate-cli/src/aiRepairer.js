const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
require('dotenv').config();

// 尝试导入 AI 依赖，如果失败则禁用 AI 功能
let generateText, createOpenAI;
let aiAvailable = false;

try {
  const ai = require('ai');
  const aiSdkOpenai = require('@ai-sdk/openai');
  generateText = ai.generateText;
  createOpenAI = aiSdkOpenai.createOpenAI;
  aiAvailable = true;
} catch (error) {
  console.warn(chalk.yellow('⚠️  AI 依赖未安装，AI 修复功能将被禁用'));
  console.warn(chalk.gray('   运行 "npm install ai @ai-sdk/openai" 来启用 AI 功能'));
  aiAvailable = false;
}

/**
 * Configure LLM provider based on available environment variables
 */
function configureLLMProvider() {
  // Priority order: DeepSeek -> GLM -> OpenAI (DeepSeek prioritized as requested)

  // DeepSeek Provider (Prioritized)
  if (process.env.DEEPSEEK_TOKEN) {
    const openai = createOpenAI({
      compatibility: "compatible",
      baseURL: process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com/v1",
      apiKey: process.env.DEEPSEEK_TOKEN,
    });

    return {
      fullModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
      quickModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
      openai,
      providerName: "DeepSeek"
    };
  }

  // GLM Provider (智谱AI)
  if (process.env.GLM_TOKEN) {
    const openai = createOpenAI({
      compatibility: "compatible",
      baseURL: process.env.LLM_BASE_URL || "https://open.bigmodel.cn/api/paas/v4",
      apiKey: process.env.GLM_TOKEN,
    });

    return {
      fullModel: process.env.LLM_MODEL || "glm-4-air",
      quickModel: process.env.LLM_MODEL || "glm-4-air",
      openai,
      providerName: "GLM"
    };
  }

  // OpenAI Provider
  if (process.env.OPENAI_API_KEY) {
    const openai = createOpenAI({
      compatibility: "strict",
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL,
    });

    return {
      fullModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
      quickModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
      openai,
      providerName: "OpenAI"
    };
  }

  return null;
}

/**
 * Check if any LLM provider is available
 */
function hasLLMProvider() {
  return configureLLMProvider() !== null;
}

/**
 * Get provider status for debugging
 */
function getLLMProviderStatus() {
  return {
    GLM: !!process.env.GLM_TOKEN,
    DeepSeek: !!process.env.DEEPSEEK_TOKEN,
    OpenAI: !!process.env.OPENAI_API_KEY,
    Anthropic: !!process.env.ANTHROPIC_API_KEY
  };
}

/**
 * AI 代码修复器
 */
class AIRepairer {
  constructor(options = {}) {
    this.options = {
      maxTokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.1,
      maxRetries: options.maxRetries || 3,
      ...options
    };

    // 配置 LLM 提供商
    this.llmConfig = null;
    if (aiAvailable) {
      this.llmConfig = configureLLMProvider();
      if (this.llmConfig) {
        this.enabled = true;
        console.log(chalk.green(`✅ AI 修复功能已启用 (${this.llmConfig.providerName})`));
      } else {
        this.enabled = false;
        console.warn(chalk.yellow('⚠️  未找到可用的 LLM 提供商，AI 修复功能将被禁用'));
        console.warn(chalk.gray('   请设置以下环境变量之一: DEEPSEEK_TOKEN, GLM_TOKEN, OPENAI_API_KEY'));
      }
    } else {
      this.enabled = false;
    }

    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 修复失败的文件列表
   */
  async repairFailedFiles(failedFiles, projectPath) {
    if (!this.enabled) {
      console.log(chalk.yellow('⚠️  AI 修复功能未启用，跳过修复步骤'));
      return { success: false, reason: 'AI repair disabled' };
    }

    console.log(chalk.blue(`🤖 开始使用 AI 修复 ${failedFiles.length} 个失败文件...`));

    const results = [];

    for (const failedFile of failedFiles) {
      try {
        const result = await this.repairSingleFile(failedFile, projectPath);
        results.push(result);
        
        if (result.success) {
          this.stats.success++;
        } else {
          this.stats.failed++;
        }
      } catch (error) {
        console.error(chalk.red(`❌ 修复文件失败: ${failedFile.file}`), error.message);
        this.stats.failed++;
        results.push({
          file: failedFile.file,
          success: false,
          error: error.message
        });
      }
      
      this.stats.attempted++;
    }

    this.printRepairStats();
    return results;
  }

  /**
   * 修复单个文件
   */
  async repairSingleFile(failedFile, projectPath) {
    const filePath = path.isAbsolute(failedFile.absolutePath) 
      ? failedFile.absolutePath 
      : path.join(projectPath, failedFile.file);

    console.log(chalk.gray(`🔧 修复: ${failedFile.file}...`));

    try {
      // 读取原始文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');
      
      // 生成修复提示
      const prompt = this.generateRepairPrompt(originalContent, failedFile);
      
      // 调用 AI 进行修复
      const repairedContent = await this.callAI(prompt);
      
      // 验证修复结果
      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // 备份原文件
        await this.backupFile(filePath);
        
        // 写入修复后的内容
        await fs.writeFile(filePath, repairedContent, 'utf8');
        
        console.log(chalk.green(`✅ 修复成功: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  修复结果验证失败: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (error) {
      console.log(chalk.red(`❌ 修复失败: ${failedFile.file}`));
      return {
        file: failedFile.file,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成修复提示
   */
  generateRepairPrompt(originalContent, failedFile) {
    const fileExtension = path.extname(failedFile.file);
    const errorMessage = failedFile.error;

    let prompt = `将如下的 Vue 2 + Element UI 组件代码，迁移到 Vue 3 + Element Plus 组件代码

**迁移要求**:
1. 状态管理：如果原来的代码使用 Vuex 状态管理，请转换为 Vuex 4.x 语法，如果没有用到状态管理就不要添加；
2. 功能复刻：不要求实现一样，但是输入参数必须一致；
3. Vue 3 语法：使用 Vue 3 的 Composition API 或 Options API；
4. Element Plus：将 Element UI 组件替换为 Element Plus 组件；
5. 事件处理：更新事件处理方式以兼容 Vue 3；
6. 响应式数据：使用 Vue 3 的响应式 API；

**原始错误**: ${errorMessage}

**原始代码**:
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

请使用 ${fileExtension.slice(1)} 代码块返回修复后的完整代码，方便我直接使用。`;

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定要求**:
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 在 <script> 中使用 defineComponent 或 Composition API
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript 文件特定要求**:
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句`;
    }

    return prompt;
  }

  /**
   * 调用 AI API
   */
  async callAI(prompt) {
    if (!aiAvailable || !this.llmConfig) {
      throw new Error('AI 功能不可用，请安装相关依赖或配置 LLM 提供商');
    }

    let lastError;

    for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
      try {
        console.log(chalk.gray(`🤖 调用 ${this.llmConfig.providerName} API (尝试 ${attempt}/${this.options.maxRetries})...`));

        const { text } = await generateText({
          model: this.llmConfig.openai(this.llmConfig.fullModel),
          prompt: prompt,
          maxTokens: this.options.maxTokens,
          temperature: this.options.temperature,
        });

        // 提取代码块
        const codeMatch = text.match(/```[\w]*\n([\s\S]*?)\n```/);
        if (codeMatch) {
          return codeMatch[1];
        } else {
          // 如果没有代码块，返回整个响应
          return text.trim();
        }
      } catch (error) {
        lastError = error;
        console.log(chalk.yellow(`⚠️  AI 调用失败 (尝试 ${attempt}/${this.options.maxRetries}): ${error.message}`));
        
        if (attempt < this.options.maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw new Error(`AI 调用失败，已重试 ${this.options.maxRetries} 次: ${lastError.message}`);
  }

  /**
   * 验证修复后的内容
   */
  validateRepairedContent(repairedContent, originalContent) {
    // 基本验证
    if (!repairedContent || repairedContent.trim().length === 0) {
      return false;
    }

    // 检查是否有明显的错误标记
    const errorMarkers = ['ERROR', 'FIXME', 'TODO: Fix', '// BROKEN'];
    if (errorMarkers.some(marker => repairedContent.includes(marker))) {
      return false;
    }

    // 检查代码长度是否合理（不应该太短或太长）
    const lengthRatio = repairedContent.length / originalContent.length;
    if (lengthRatio < 0.3 || lengthRatio > 3) {
      return false;
    }

    // 检查是否包含基本的代码结构
    if (originalContent.includes('export default') && !repairedContent.includes('export')) {
      return false;
    }

    return true;
  }

  /**
   * 备份文件
   */
  async backupFile(filePath) {
    const backupPath = `${filePath}.ai-backup`;
    await fs.copy(filePath, backupPath);
  }

  /**
   * 打印修复统计
   */
  printRepairStats() {
    console.log('\n' + chalk.bold('🤖 AI 修复统计:'));
    console.log(`尝试修复: ${this.stats.attempted} 个文件`);
    console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));
    
    if (this.stats.attempted > 0) {
      const successRate = ((this.stats.success / this.stats.attempted) * 100).toFixed(1);
      console.log(chalk.bold(`成功率: ${successRate}%`));
    }
  }

  /**
   * 检查 AI 功能是否可用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

module.exports = AIRepairer;
