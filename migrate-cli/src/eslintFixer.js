const { execSync, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * ESLint 自动修复器
 */
class ESLintFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      configFile: '.eslintrc.js',
      srcDir: 'src',
      extensions: ['.js', '.vue', '.ts'],
      fixableOnly: true,
      ...options
    };

    this.stats = {
      filesChecked: 0,
      filesFixed: 0,
      errorsFixed: 0,
      warningsFixed: 0,
      remainingErrors: 0,
      remainingWarnings: 0
    };
  }

  /**
   * 执行 ESLint 自动修复
   */
  async fix() {
    try {
      console.log(chalk.blue('🔧 开始 ESLint 自动修复...'));

      // 检查 ESLint 配置
      await this.ensureESLintConfig();

      // 执行修复
      const result = await this.runESLintFix();

      // 运行检查获取统计信息
      await this.runESLintCheck();

      this.printFixStats();

      return result;
    } catch (error) {
      console.error(chalk.red('❌ ESLint 修复失败:'), error.message);
      throw error;
    }
  }

  /**
   * 确保 ESLint 配置存在
   */
  async ensureESLintConfig() {
    const configPath = path.join(this.projectPath, this.options.configFile);

    if (!await fs.pathExists(configPath)) {
      console.log(chalk.yellow('📝 创建 ESLint 配置文件...'));
      await this.createESLintConfig(configPath);
    } else {
      console.log(chalk.gray('✅ ESLint 配置文件已存在'));
    }
  }

  /**
   * 创建 ESLint 配置文件
   */
  async createESLintConfig(configPath) {
    const config = {
      root: true,
      env: {
        node: true,
        browser: true,
        es2022: true
      },
      extends: [
        'eslint:recommended',
        '@vue/eslint-config-standard',
        'plugin:vue/vue3-recommended'
      ],
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module'
      },
      plugins: ['vue'],
      rules: {
        // Vue 3 特定规则
        'vue/no-deprecated-slot-attribute': 'error',
        'vue/no-deprecated-slot-scope-attribute': 'error',
        'vue/no-deprecated-scope-attribute': 'error',
        'vue/no-deprecated-v-on-native-modifier': 'error',
        'vue/no-deprecated-v-bind-sync': 'error',
        'vue/no-deprecated-dollar-listeners-api': 'error',
        'vue/no-deprecated-events-api': 'error',
        'vue/no-deprecated-filter': 'error',
        'vue/no-deprecated-functional-template': 'error',
        'vue/no-deprecated-html-element-is': 'error',
        'vue/no-deprecated-inline-template': 'error',
        'vue/no-deprecated-props-default-this': 'error',
        'vue/no-deprecated-router-link-tag-prop': 'error',
        'vue/no-deprecated-v-is': 'error',
        'vue/no-deprecated-v-on-number-modifiers': 'error',
        'vue/no-deprecated-vue-config-keycodes': 'error',

        // 代码质量规则
        'no-console': 'warn',
        'no-debugger': 'warn',
        'no-unused-vars': 'warn',
        'prefer-const': 'error',
        'no-var': 'error',

        // 格式化规则（可自动修复）
        'indent': ['error', 2],
        'quotes': ['error', 'single'],
        'semi': ['error', 'never'],
        'comma-dangle': ['error', 'never'],
        'space-before-function-paren': ['error', 'always'],
        'object-curly-spacing': ['error', 'always'],
        'array-bracket-spacing': ['error', 'never'],
        'computed-property-spacing': ['error', 'never'],
        'key-spacing': ['error', { beforeColon: false, afterColon: true }],
        'keyword-spacing': ['error', { before: true, after: true }],
        'space-infix-ops': 'error',
        'space-unary-ops': 'error',
        'spaced-comment': ['error', 'always'],
        'eol-last': ['error', 'always'],
        'no-trailing-spaces': 'error',
        'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }]
      },
      overrides: [
        {
          files: ['*.vue'],
          parser: 'vue-eslint-parser',
          parserOptions: {
            parser: '@babel/eslint-parser',
            requireConfigFile: false,
            babelOptions: {
              presets: ['@babel/preset-env']
            }
          }
        }
      ]
    };

    const configContent = `module.exports = ${JSON.stringify(config, null, 2)}`;
    await fs.writeFile(configPath, configContent);
    console.log(chalk.green(`✅ 已创建 ESLint 配置: ${configPath}`));
  }

  /**
   * 运行 ESLint 修复
   */
  async runESLintFix() {
    const srcPath = path.join(this.projectPath, this.options.srcDir);
    const extensions = this.options.extensions.join(',');

    const command = [
      'npx', 'eslint',
      srcPath,
      '--ext', extensions,
      '--fix',
      '--format', 'json'
    ];

    if (this.options.fixableOnly) {
      command.push('--fix-type', 'problem,suggestion,layout');
    }

    try {
      console.log(chalk.gray('运行 ESLint 修复...'));

      const result = execSync(command.join(' '), {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // ESLint 返回 JSON 格式的结果
      try {
        const eslintResult = JSON.parse(result);
        this.processESLintResult(eslintResult, 'fix');
        return eslintResult;
      } catch (parseError) {
        // 如果没有输出或输出不是 JSON，说明没有问题
        console.log(chalk.green('✅ ESLint 修复完成，没有发现问题'));
        return [];
      }
    } catch (error) {
      // ESLint 返回非零退出码时会抛出错误，但这是正常的
      if (error.stdout) {
        try {
          const eslintResult = JSON.parse(error.stdout);
          this.processESLintResult(eslintResult, 'fix');
          return eslintResult;
        } catch (parseError) {
          console.log(chalk.yellow('⚠️  ESLint 输出解析失败，但修复可能已完成'));
          return [];
        }
      }
      throw error;
    }
  }

  /**
   * 运行 ESLint 检查（不修复）
   */
  async runESLintCheck() {
    const srcPath = path.join(this.projectPath, this.options.srcDir);
    const extensions = this.options.extensions.join(',');

    const command = [
      'npx', 'eslint',
      srcPath,
      '--ext', extensions,
      '--format', 'json'
    ];

    try {
      const result = execSync(command.join(' '), {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });

      try {
        const eslintResult = JSON.parse(result);
        this.processESLintResult(eslintResult, 'check');
        return eslintResult;
      } catch (parseError) {
        console.log(chalk.green('✅ 没有发现 ESLint 问题'));
        return [];
      }
    } catch (error) {
      if (error.stdout) {
        try {
          const eslintResult = JSON.parse(error.stdout);
          this.processESLintResult(eslintResult, 'check');
          return eslintResult;
        } catch (parseError) {
          console.log(chalk.yellow('⚠️  ESLint 检查结果解析失败'));
          return [];
        }
      }
      console.log(chalk.yellow('⚠️  ESLint 检查失败，可能是配置问题'));
      return [];
    }
  }

  /**
   * 处理 ESLint 结果
   */
  processESLintResult(results, type) {
    let totalErrors = 0;
    let totalWarnings = 0;
    let filesWithIssues = 0;

    results.forEach(result => {
      if (result.messages && result.messages.length > 0) {
        filesWithIssues++;

        result.messages.forEach(message => {
          if (message.severity === 2) {
            totalErrors++;
          } else if (message.severity === 1) {
            totalWarnings++;
          }
        });
      }
    });

    if (type === 'fix') {
      this.stats.filesChecked = results.length;
      this.stats.filesFixed = filesWithIssues;
    } else if (type === 'check') {
      this.stats.remainingErrors = totalErrors;
      this.stats.remainingWarnings = totalWarnings;
    }

    if (filesWithIssues > 0) {
      console.log(chalk.yellow(`发现 ${filesWithIssues} 个文件有问题 (${totalErrors} 错误, ${totalWarnings} 警告)`));
    }
  }

  /**
   * 打印修复统计
   */
  printFixStats() {
    console.log('\n' + chalk.bold('🔧 ESLint 修复统计:'));
    console.log(`检查文件: ${this.stats.filesChecked} 个`);
    console.log(`修复文件: ${this.stats.filesFixed} 个`);

    if (this.stats.remainingErrors > 0 || this.stats.remainingWarnings > 0) {
      console.log(chalk.yellow(`剩余问题: ${this.stats.remainingErrors} 错误, ${this.stats.remainingWarnings} 警告`));
      console.log(chalk.gray('💡 某些问题需要手动修复'));
    } else {
      console.log(chalk.green('✅ 所有可自动修复的问题已解决'));
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 检查 ESLint 是否可用
   */
  async isESLintAvailable() {
    try {
      execSync('npx eslint --version', {
        cwd: this.projectPath,
        stdio: 'ignore'
      });
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = ESLintFixer;
