const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const glob = require('glob')
const { spawn } = require('child_process')

/**
 * Vue 代码迁移器
 */
class CodeMigrator {
	constructor (projectPath, options = {}) {
		this.projectPath = projectPath
		this.options = {
			srcDir: 'src',
			outputDir: null, // null 表示原地修改
			backupDir: 'backup',
			includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
			excludePatterns: ['node_modules/**', 'dist/**', 'build/**'],
			...options
		}

		this.stats = {
			total: 0,
			success: 0,
			failed: 0,
			skipped: 0,
			failedFiles: []
		}
	}

	/**
	 * 执行代码迁移
	 */
	async migrate () {
		try {
			console.log(chalk.blue('🔄 开始 Vue 代码迁移...'))

			// 创建备份
			await this.createBackup()

			// 使用 CLI 方式进行批量迁移
			await this.migrateWithCLI()

			// 获取迁移后的统计信息
			await this.collectMigrationStats()

			// 打印统计结果
			this.printMigrationStats()

			return this.stats
		} catch (error) {
			console.error(chalk.red('❌ 代码迁移失败:'), error.message)
			throw error
		}
	}

	/**
	 * 创建备份
	 */
	async createBackup () {
		const backupPath = path.join(this.projectPath, this.options.backupDir)
		const srcPath = path.join(this.projectPath, this.options.srcDir)

		if (await fs.pathExists(srcPath)) {
			await fs.ensureDir(backupPath)
			await fs.copy(srcPath, path.join(backupPath, this.options.srcDir))
			console.log(chalk.yellow(`📋 已创建代码备份: ${backupPath}`))
		}
	}

	/**
	 * 使用 CLI 方式进行迁移
	 */
	async migrateWithCLI () {
		const srcPath = path.join(this.projectPath, this.options.srcDir)

		console.log(chalk.blue('🔄 使用 gogocode CLI 进行 Vue 迁移...'))

		// 执行 Vue 插件迁移
		await this.executeGogocodeCLI(srcPath, 'gogocode-plugin-vue')

		console.log(chalk.blue('🔄 使用 gogocode CLI 进行 Element UI 迁移...'))

		// 执行 Element UI 插件迁移
		await this.executeGogocodeCLI(srcPath, 'gogocode-plugin-element')
	}

	/**
	 * 执行 gogocode CLI 命令
	 */
	async executeGogocodeCLI (sourcePath, transformPlugin) {
		return new Promise((resolve, reject) => {
			const command = 'gogocode'
			const args = [
				'-s', sourcePath,
				'-t', transformPlugin,
				'-o', sourcePath
			]

			console.log(chalk.gray(`执行命令: ${command} ${args.join(' ')}`))

			const child = spawn(command, args, {
				stdio: 'pipe',
				cwd: this.projectPath
			})

			let stdout = ''
			let stderr = ''

			child.stdout.on('data', (data) => {
				stdout += data.toString()
				// 实时输出进度
				process.stdout.write('.')
			})

			child.stderr.on('data', (data) => {
				stderr += data.toString()
			})

			child.on('close', (code) => {
				console.log('') // 换行

				if (code === 0) {
					console.log(chalk.green(`✅ ${transformPlugin} 迁移完成`))
					if (stdout.trim()) {
						console.log(chalk.gray('输出:'), stdout.trim())
					}
					resolve({ stdout, stderr })
				} else {
					console.log(chalk.red(`❌ ${transformPlugin} 迁移失败 (退出码: ${code})`))
					if (stderr.trim()) {
						console.log(chalk.red('错误:'), stderr.trim())
					}
					reject(new Error(`gogocode CLI 执行失败: ${stderr || '未知错误'}`))
				}
			})

			child.on('error', (error) => {
				console.log('') // 换行
				console.log(chalk.red(`❌ 无法执行 gogocode 命令: ${error.message}`))
				reject(error)
			})
		})
	}

	/**
	 * 收集迁移统计信息
	 */
	async collectMigrationStats () {
		const files = await this.getFilesToMigrate()
		this.stats.total = files.length

		// 简单统计：假设所有文件都成功迁移了
		// 在实际使用中，可以通过检查文件修改时间或其他方式来判断
		this.stats.success = files.length
		this.stats.failed = 0
		this.stats.skipped = 0
		this.stats.failedFiles = []

		console.log(chalk.gray(`统计: 处理了 ${files.length} 个文件`))
	}

	/**
	 * 获取需要迁移的文件列表
	 */
	async getFilesToMigrate () {
		const srcPath = path.join(this.projectPath, this.options.srcDir)
		const files = []

		for (const pattern of this.options.includePatterns) {
			const matchedFiles = glob.sync(pattern, {
				cwd: srcPath,
				ignore: this.options.excludePatterns,
				absolute: false
			})

			files.push(...matchedFiles.map(file => path.join(srcPath, file)))
		}

		// 去重
		return [...new Set(files)]
	}

	/**
	 * 迁移单个文件 (已弃用 - 现在使用 CLI 方式)
	 * 保留此方法以备将来需要单独处理特定文件时使用
	 */
	async migrateFile (filePath) {
		// 此方法已被 migrateWithCLI 替代
		// 如果需要单独处理文件，可以在这里实现
		console.log(chalk.yellow(`单文件迁移已弃用，请使用 CLI 批量迁移: ${filePath}`))
	}

	/**
	 * 迁移 Vue 文件 (已弃用 - 现在使用 CLI 方式)
	 * 保留此方法以备将来需要自定义转换时使用
	 */
	async migrateVueFile (source, filePath) {
		// 此方法已被 CLI 方式替代
		console.log(chalk.yellow(`Vue 文件迁移已改为 CLI 方式: ${filePath}`))
		return source
	}

	/**
	 * 迁移 JS/TS 文件 (已弃用 - 现在使用 CLI 方式)
	 * 保留此方法以备将来需要自定义转换时使用
	 */
	async migrateJsFile (source, filePath) {
		// 此方法已被 CLI 方式替代
		console.log(chalk.yellow(`JS 文件迁移已改为 CLI 方式: ${filePath}`))
		return source
	}

	/**
	 * 应用自定义 Vue 转换规则 (已弃用 - 现在使用 CLI 插件)
	 * 保留此方法以备将来需要额外的自定义转换时使用
	 */
	applyCustomVueTransforms (code) {
		// 这些转换现在由 gogocode-plugin-vue 和 gogocode-plugin-element 处理
		console.log(chalk.gray('自定义 Vue 转换已由 CLI 插件处理'))
		return code
	}

	/**
	 * 应用 JS 转换规则 (已弃用 - 现在使用 CLI 插件)
	 * 保留此方法以备将来需要额外的自定义转换时使用
	 */
	applyJsTransforms (ast) {
		// 这些转换现在由 gogocode-plugin-vue 处理
		console.log(chalk.gray('自定义 JS 转换已由 CLI 插件处理'))
		return ast
	}

	/**
	 * 打印迁移统计
	 */
	printMigrationStats () {
		console.log('\n' + chalk.bold('📊 代码迁移统计:'))
		console.log(`总计: ${this.stats.total} 个文件`)
		console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`))
		console.log(chalk.gray(`⏸️  跳过: ${this.stats.skipped} 个`))
		console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`))

		if (this.stats.failedFiles.length > 0) {
			console.log(chalk.red('\n失败的文件:'))
			this.stats.failedFiles.forEach(({ file, error }) => {
				const relativePath = path.relative(this.projectPath, file)
				console.log(`  ${relativePath}: ${error}`)
			})
		}

		const successRate = ((this.stats.success / this.stats.total) * 100).toFixed(1)
		console.log(chalk.bold(`\n成功率: ${successRate}%`))
	}

	/**
	 * 分类错误类型
	 */
	categorizeError (errorMessage) {
		if (errorMessage.includes('eventsApi') || errorMessage.includes('gogocode-plugin-vue')) {
			return 'gogocode-events-api'
		} else if (errorMessage.includes('gogocode')) {
			return 'gogocode-general'
		} else if (errorMessage.includes('SyntaxError')) {
			return 'syntax-error'
		} else if (errorMessage.includes('Cannot read properties')) {
			return 'property-access-error'
		} else {
			return 'unknown'
		}
	}

	/**
	 * 获取失败的文件列表
	 */
	getFailedFiles () {
		return this.stats.failedFiles
	}
}

module.exports = CodeMigrator
