# CLI 迁移变更日志

## 概述

将 `codeMigrator.js` 从使用 gogocode API 方式改为使用 CLI 方式，以解决 API 调用时可能遇到的问题。

## 主要变更

### 1. 代码变更

#### `src/codeMigrator.js`
- ✅ 移除了直接的 gogocode API 导入
- ✅ 新增了 `migrateWithCLI()` 方法
- ✅ 新增了 `executeGogocodeCLI()` 方法  
- ✅ 新增了 `collectMigrationStats()` 方法
- ✅ 保留了原有方法但标记为已弃用
- ✅ 使用 `child_process.spawn` 执行 CLI 命令

#### `package.json`
- ✅ 将 gogocode 相关依赖移至 `peerDependencies`
- ✅ 新增了 `install-gogocode` 脚本
- ✅ 新增了 `test-cli` 脚本
- ✅ 新增了 `postinstall` 提示

### 2. 新增文件

#### `install-gogocode.js`
- ✅ 自动安装全局 gogocode 依赖的脚本
- ✅ 包含安装验证功能
- ✅ 提供详细的错误处理和提示

#### `test-cli-migration.js`
- ✅ 测试 CLI 迁移功能的脚本
- ✅ 包含错误处理和使用提示

#### `CLI-MIGRATION.md`
- ✅ 详细的 CLI 迁移使用文档
- ✅ 包含前置要求、使用方法、故障排除等

#### `CHANGELOG-CLI.md`
- ✅ 本变更日志文件

## 执行的命令

新的 CLI 方式会依次执行以下命令：

```bash
# Vue 2 到 Vue 3 迁移
gogocode -s ./src -t gogocode-plugin-vue -o ./src

# Element UI 到 Element Plus 迁移
gogocode -s ./src -t gogocode-plugin-element -o ./src
```

## 使用方法

### 1. 安装全局依赖

```bash
# 方式一：使用提供的脚本
npm run install-gogocode

# 方式二：手动安装
npm install -g gogocode gogocode-plugin-vue gogocode-plugin-element
```

### 2. 测试迁移

```bash
npm run test-cli
```

### 3. 正常使用

```javascript
const CodeMigrator = require('./src/codeMigrator')

const migrator = new CodeMigrator('/path/to/project')
await migrator.migrate()
```

## 优势

### 相比 API 方式

1. **更稳定**: 避免 API 调用的内存问题
2. **更直观**: 使用官方推荐的 CLI 方式
3. **更好的错误信息**: CLI 输出更清晰
4. **批量处理**: 一次性处理整个目录

### 保留的功能

1. **备份功能**: 迁移前自动创建备份
2. **统计功能**: 提供迁移统计信息
3. **错误处理**: 完整的错误处理和日志
4. **配置选项**: 支持自定义配置

## 兼容性

- ✅ 保留了原有的 API 接口
- ✅ 原有的调用方式仍然有效
- ✅ 只是内部实现改为 CLI 方式
- ✅ 统计结果格式保持一致

## 注意事项

1. **全局依赖**: 需要全局安装 gogocode 和插件
2. **权限问题**: 可能需要管理员权限安装全局包
3. **版本兼容**: 确保使用兼容的插件版本
4. **备份重要**: 迁移前务必备份重要文件

## 故障排除

### 常见错误

1. **`spawn gogocode ENOENT`**
   - 原因: gogocode 未全局安装
   - 解决: `npm install -g gogocode`

2. **`Cannot find module 'gogocode-plugin-vue'`**
   - 原因: 插件未全局安装
   - 解决: `npm install -g gogocode-plugin-vue`

3. **权限错误**
   - 原因: 没有全局安装权限
   - 解决: 使用 `sudo` 或配置 npm 权限

### 验证方法

```bash
# 检查 gogocode 安装
gogocode --version

# 检查插件安装
npm list -g gogocode-plugin-vue
npm list -g gogocode-plugin-element
```

## 后续计划

1. **增强统计**: 通过文件修改时间提供更准确统计
2. **并行处理**: 并行执行多个插件提高效率
3. **自定义插件**: 支持用户自定义插件
4. **增量迁移**: 支持只迁移变更文件
5. **配置文件**: 支持配置文件方式设置参数

## 测试建议

1. 先在小项目上测试
2. 确保备份重要文件
3. 检查迁移结果的正确性
4. 运行项目确保功能正常
