#!/usr/bin/env node

const { spawn } = require('child_process')
const chalk = require('chalk')

/**
 * 安装 gogocode 全局依赖的脚本
 */
async function installGogocode() {
    console.log(chalk.blue('🔧 安装 gogocode 全局依赖...\n'))
    
    const packages = [
        'gogocode',
        'gogocode-plugin-vue', 
        'gogocode-plugin-element'
    ]
    
    for (const pkg of packages) {
        console.log(chalk.gray(`正在安装 ${pkg}...`))
        
        try {
            await installPackage(pkg)
            console.log(chalk.green(`✅ ${pkg} 安装成功`))
        } catch (error) {
            console.log(chalk.red(`❌ ${pkg} 安装失败: ${error.message}`))
            
            // 给出替代方案
            console.log(chalk.yellow(`请手动安装: npm install -g ${pkg}`))
        }
    }
    
    console.log(chalk.blue('\n🔍 验证安装...'))
    
    // 验证安装
    try {
        await verifyInstallation()
        console.log(chalk.green('\n✅ 所有依赖安装成功！'))
        console.log(chalk.gray('现在可以使用 CLI 方式进行代码迁移了。'))
    } catch (error) {
        console.log(chalk.red('\n❌ 验证失败，请检查安装'))
        console.log(chalk.yellow('请手动执行以下命令:'))
        console.log('  npm install -g gogocode')
        console.log('  npm install -g gogocode-plugin-vue')
        console.log('  npm install -g gogocode-plugin-element')
    }
}

/**
 * 安装单个包
 */
function installPackage(packageName) {
    return new Promise((resolve, reject) => {
        const child = spawn('npm', ['install', '-g', packageName], {
            stdio: 'pipe'
        })
        
        let stderr = ''
        
        child.stderr.on('data', (data) => {
            stderr += data.toString()
        })
        
        child.on('close', (code) => {
            if (code === 0) {
                resolve()
            } else {
                reject(new Error(stderr || `安装失败，退出码: ${code}`))
            }
        })
        
        child.on('error', (error) => {
            reject(error)
        })
    })
}

/**
 * 验证安装
 */
function verifyInstallation() {
    return new Promise((resolve, reject) => {
        const child = spawn('gogocode', ['--version'], {
            stdio: 'pipe'
        })
        
        child.on('close', (code) => {
            if (code === 0) {
                resolve()
            } else {
                reject(new Error('gogocode 命令不可用'))
            }
        })
        
        child.on('error', (error) => {
            reject(error)
        })
    })
}

// 如果直接运行此脚本
if (require.main === module) {
    installGogocode().catch(error => {
        console.error(chalk.red('安装过程出错:'), error.message)
        process.exit(1)
    })
}

module.exports = installGogocode
