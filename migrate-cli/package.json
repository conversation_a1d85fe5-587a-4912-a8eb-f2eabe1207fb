{"name": "vue-migration-tools", "version": "1.0.0", "description": "Vue 2 to Vue 3 migration tools", "main": "index.js", "bin": {"vue2to3-migrator": "./bin/cli.js", "vue-migrator": "./bin/vue-migrator.js"}, "scripts": {"migrate": "node index.js", "test": "node test.js", "test-cli": "node test-cli-migration.js", "install-gogocode": "node install-gogocode.js", "postinstall": "echo '请运行 npm run install-gogocode 安装全局依赖'"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@vue/eslint-config-standard": "^8.0.1", "ai": "^3.4.33", "axios": "^1.7.7", "chalk": "^4.1.2", "commander": "^11.1.0", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.28.0", "fs-extra": "^11.2.0", "glob": "^10.4.5", "ora": "^5.4.1", "semver": "^7.6.3"}, "peerDependencies": {"gogocode": "^1.0.55", "gogocode-plugin-element": "latest", "gogocode-plugin-vue": "latest"}, "peerDependenciesMeta": {"gogocode": {"optional": false}, "gogocode-plugin-element": {"optional": false}, "gogocode-plugin-vue": {"optional": false}}}