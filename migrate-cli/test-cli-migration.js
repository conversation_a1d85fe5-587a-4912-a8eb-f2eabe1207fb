#!/usr/bin/env node

const path = require('path')
const CodeMigrator = require('./src/codeMigrator')

/**
 * 测试 CLI 方式的代码迁移
 */
async function testCLIMigration() {
    console.log('🧪 测试 CLI 方式的代码迁移...\n')
    
    // 假设我们要迁移当前目录的上级目录中的一个项目
    const projectPath = path.resolve(__dirname, '../aup-admin-ui')
    
    console.log(`项目路径: ${projectPath}`)
    
    const migrator = new CodeMigrator(projectPath, {
        srcDir: 'src',
        backupDir: 'backup-cli-test',
        includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
        excludePatterns: ['node_modules/**', 'dist/**', 'build/**', 'backup*/**']
    })
    
    try {
        const stats = await migrator.migrate()
        
        console.log('\n✅ 迁移测试完成!')
        console.log('统计结果:', stats)
        
    } catch (error) {
        console.error('\n❌ 迁移测试失败:', error.message)
        
        // 如果是 gogocode 命令不存在的错误，给出提示
        if (error.message.includes('ENOENT') || error.message.includes('command not found')) {
            console.log('\n💡 提示:')
            console.log('请确保已全局安装 gogocode:')
            console.log('  npm install -g gogocode')
            console.log('  npm install -g gogocode-plugin-vue')
            console.log('  npm install -g gogocode-plugin-element')
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    testCLIMigration()
}

module.exports = testCLIMigration
